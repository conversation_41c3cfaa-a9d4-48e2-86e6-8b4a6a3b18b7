/* CSS Variables for Light/Dark Mode */
:root {
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #333333;
    --text-primary: #000000;
    --text-secondary: #666666;
    --background-primary: #ffffff;
    --background-secondary: #f8f8f8;
    --border-color: #e0e0e0;
    --shadow: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
    --primary-color: #ffffff;
    --secondary-color: #000000;
    --accent-color: #cccccc;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --background-primary: #000000;
    --background-secondary: #111111;
    --border-color: #333333;
    --shadow: rgba(255, 255, 255, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    transition: all 0.3s ease;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--background-primary);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo a {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--text-primary);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background: var(--background-secondary);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    background: var(--background-primary);
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 300;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn {
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-color);
}

.btn-primary {
    background: var(--primary-color);
    color: var(--secondary-color);
}

.btn-primary:hover {
    background: transparent;
    color: var(--primary-color);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    font-weight: 300;
    color: var(--text-primary);
}

/* About Section */
.about {
    background: var(--background-secondary);
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.8;
}

.about-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 3rem;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat p {
    color: var(--text-secondary);
}

/* Timeline */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 2px;
    background: var(--border-color);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -1px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    background: inherit;
    width: 50%;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    top: 15px;
    right: -10px;
    z-index: 1;
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -10px;
}

.timeline-content {
    padding: 20px 30px;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    box-shadow: 0 4px 6px var(--shadow);
}

.timeline-content h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.timeline-content h4 {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.timeline-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: bold;
}

/* Skills Section */
.skills {
    background: var(--background-secondary);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 3rem;
}

.skill-card {
    background: var(--background-primary);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.skill-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--shadow);
}

.skill-icon {
    font-size: 3rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.skill-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.skill-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Photography Section */
.photography-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.photography-intro p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 3rem;
}

.photo-item {
    aspect-ratio: 1;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.photo-placeholder {
    width: 100%;
    height: 100%;
    background: var(--background-secondary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.photo-placeholder:hover {
    background: var(--accent-color);
}

.photo-placeholder i {
    font-size: 2rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.photo-placeholder p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Contact Section */
.contact {
    background: var(--background-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    margin-top: 3rem;
}

.contact-info h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.contact-methods {
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.contact-method i {
    margin-right: 1rem;
    width: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--background-primary);
    color: var(--text-primary);
    border-radius: 50%;
    text-decoration: none;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--background-primary);
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background: var(--background-primary);
    border-top: 1px solid var(--border-color);
    padding: 30px 0;
    text-align: center;
}

.footer p {
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .timeline::after {
        left: 31px;
    }
    
    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .timeline-item:nth-child(even) {
        left: 0%;
    }
    
    .timeline-dot {
        left: 21px;
    }
    
    .timeline-item:nth-child(even) .timeline-dot {
        left: 21px;
    }
    
    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 2rem;
    }
}
