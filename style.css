/* CSS Variables for Light/Dark Mode */
:root {
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #333333;
    --text-primary: #000000;
    --text-secondary: #666666;
    --background-primary: #ffffff;
    --background-secondary: #f8f8f8;
    --border-color: #e0e0e0;
    --shadow: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
    --primary-color: #ffffff;
    --secondary-color: #000000;
    --accent-color: #cccccc;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --background-primary: #000000;
    --background-secondary: #111111;
    --border-color: #333333;
    --shadow: rgba(255, 255, 255, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    transition: all 0.3s ease;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--background-primary);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo a {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--text-primary);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background: var(--background-secondary);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 300;
    margin-bottom: 1rem;
    color: var(--text-primary);
    background: linear-gradient(45deg, var(--text-primary), var(--text-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInFromLeft 1s ease-out 0.3s both;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    animation: slideInFromRight 1s ease-out 0.6s both;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    animation: fadeInUp 1s ease-out 1.2s both;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* Background Shapes */
.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1));
    animation: float 8s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 70%;
    right: 10%;
    animation-delay: 3s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 10%;
    left: 50%;
    animation-delay: 6s;
}

.btn {
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-color);
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: var(--secondary-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn span {
    margin-right: 8px;
}

.btn i {
    transition: transform 0.3s ease;
}

.btn:hover i {
    transform: translateX(5px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
    }
}

/* Section Styles */
section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 20%, rgba(0, 0, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(0, 0, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    font-weight: 300;
    color: var(--text-primary);
    position: relative;
    animation: fadeInUp 0.8s ease-out;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

/* About Section */
.about {
    background: var(--background-secondary);
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.8;
}

.about-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 3rem;
}

.stat {
    text-align: center;
    position: relative;
    padding: 20px;
    border-radius: 10px;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--shadow);
}

.stat h3 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.stat p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.stat-icon {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    color: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.animate-text {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-text:nth-child(2) {
    animation-delay: 0.3s;
}

/* Timeline */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 2px;
    background: var(--border-color);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -1px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    background: inherit;
    width: 50%;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    top: 15px;
    right: -10px;
    z-index: 1;
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -10px;
}

.timeline-content {
    padding: 20px 30px;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    box-shadow: 0 4px 6px var(--shadow);
}

.timeline-content h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.timeline-content h4 {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.timeline-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: bold;
}

/* Skills Section */
.skills {
    background: var(--background-secondary);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 3rem;
}

.skill-card {
    background: var(--background-primary);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.skill-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(0, 0, 0, 0.05), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.skill-card:hover::before {
    transform: translateX(100%);
}

.skill-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px var(--shadow);
    border-color: var(--primary-color);
}

.skill-icon {
    font-size: 3rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.skill-card:hover .skill-icon {
    transform: scale(1.1) rotate(5deg);
    color: var(--primary-color);
}

/* Progress Bars */
.skill-progress {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    margin-top: 1rem;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
    width: 0%;
    transition: width 2s ease-in-out;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.skill-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.skill-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Photography Section */
.photography-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.photography-intro p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 3rem;
}

.photo-item {
    aspect-ratio: 1;
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.photo-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.photo-item:hover::before {
    opacity: 1;
}

.photo-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--background-secondary), var(--background-primary));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;
    position: relative;
}

.photo-item:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 15px 30px var(--shadow);
}

.photo-placeholder:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

.photo-placeholder i {
    font-size: 2rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.photo-placeholder p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Contact Section */
.contact {
    background: var(--background-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    margin-top: 3rem;
}

.contact-info h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.contact-methods {
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.contact-method i {
    margin-right: 1rem;
    width: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--background-primary);
    color: var(--text-primary);
    border-radius: 50%;
    text-decoration: none;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--background-primary);
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background: var(--background-primary);
    border-top: 1px solid var(--border-color);
    padding: 30px 0;
    text-align: center;
}

.footer p {
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .timeline::after {
        left: 31px;
    }
    
    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .timeline-item:nth-child(even) {
        left: 0%;
    }
    
    .timeline-dot {
        left: 21px;
    }
    
    .timeline-item:nth-child(even) .timeline-dot {
        left: 21px;
    }
    
    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 2rem;
    }
}
