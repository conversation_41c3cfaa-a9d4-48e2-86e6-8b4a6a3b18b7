/* Premium CSS Variables */
:root {
    --primary-color: #6366f1;
    --secondary-color: #ffffff;
    --accent-color: #8b5cf6;
    --gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --background-primary: #ffffff;
    --background-secondary: #f8fafc;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow: rgba(0, 0, 0, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] {
    --primary-color: #818cf8;
    --secondary-color: #000000;
    --accent-color: #a78bfa;
    --gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --background-primary: #0f172a;
    --background-secondary: #1e293b;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow: rgba(0, 0, 0, 0.3);
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Inter', 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background:
        linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    transition: all 0.3s ease;
    overflow-x: hidden;
}

[data-theme="dark"] body {
    background:
        linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--background-primary);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo a {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--text-primary);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background: var(--background-secondary);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Premium Hero Section with Animated Background */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    background: transparent;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    animation: floatingOrbs 20s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
    pointer-events: none;
    animation: shimmerWave 12s ease-in-out infinite;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 1000px;
    padding: 0 20px;
}

.hero-content h1 {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 50%, #dbeafe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.03em;
    line-height: 1.1;
    opacity: 0;
    transform: translateY(50px) scale(0.9);
    animation: heroTitleReveal 2s cubic-bezier(0.16, 1, 0.3, 1) 0.5s forwards;
    text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .hero-content h1 {
    background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    font-weight: 600;
    letter-spacing: 0.02em;
    opacity: 0;
    transform: translateY(30px);
    animation: heroSubtitleReveal 1.5s cubic-bezier(0.16, 1, 0.3, 1) 1s forwards;
    text-shadow: 0 2px 20px rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .hero-subtitle {
    color: rgba(31, 41, 55, 0.9);
}

.hero-description {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
    opacity: 0;
    transform: translateY(20px);
    animation: heroDescReveal 1.2s cubic-bezier(0.16, 1, 0.3, 1) 1.5s forwards;
    text-shadow: 0 1px 10px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .hero-description {
    color: rgba(31, 41, 55, 0.8);
}

.hero-buttons {
    display: flex;
    gap: 24px;
    justify-content: center;
    flex-wrap: wrap;
    opacity: 0;
    transform: translateY(20px);
    animation: heroButtonsReveal 1s cubic-bezier(0.16, 1, 0.3, 1) 2s forwards;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* Background Shapes */
.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1));
    animation: float 8s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 70%;
    right: 10%;
    animation-delay: 3s;
}

/* Premium Animated Background Elements */
.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Floating Particles */
.particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 70%, transparent 100%);
    border-radius: 50%;
    animation: particleFloat 8s ease-in-out infinite;
}

[data-theme="dark"] .particle {
    background: radial-gradient(circle, rgba(31, 41, 55, 0.8) 0%, rgba(31, 41, 55, 0.2) 70%, transparent 100%);
}

.particle-1 { width: 4px; height: 4px; top: 20%; left: 10%; animation-delay: 0s; }
.particle-2 { width: 6px; height: 6px; top: 60%; left: 20%; animation-delay: 1s; }
.particle-3 { width: 3px; height: 3px; top: 40%; left: 70%; animation-delay: 2s; }
.particle-4 { width: 5px; height: 5px; top: 80%; left: 60%; animation-delay: 3s; }
.particle-5 { width: 4px; height: 4px; top: 30%; left: 80%; animation-delay: 4s; }
.particle-6 { width: 7px; height: 7px; top: 70%; left: 15%; animation-delay: 5s; }
.particle-7 { width: 3px; height: 3px; top: 50%; left: 90%; animation-delay: 6s; }
.particle-8 { width: 5px; height: 5px; top: 10%; left: 50%; animation-delay: 7s; }

/* Geometric Shapes */
.geometric-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.geo-shape {
    position: absolute;
    opacity: 0.1;
    animation: geometricRotate 20s linear infinite;
}

[data-theme="dark"] .geo-shape {
    opacity: 0.05;
}

.geo-circle-1 {
    width: 100px;
    height: 100px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    top: 15%;
    left: 15%;
    animation-delay: 0s;
}

.geo-circle-2 {
    width: 60px;
    height: 60px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    top: 70%;
    right: 20%;
    animation-delay: 5s;
}

.geo-triangle-1 {
    width: 0;
    height: 0;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-bottom: 43px solid rgba(255, 255, 255, 0.2);
    top: 40%;
    right: 10%;
    animation-delay: 10s;
}

.geo-square-1 {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.25);
    top: 60%;
    left: 5%;
    animation-delay: 15s;
}

.geo-hexagon-1 {
    width: 50px;
    height: 28.87px;
    background: rgba(255, 255, 255, 0.1);
    position: relative;
    top: 25%;
    right: 5%;
    animation-delay: 8s;
}

.geo-hexagon-1:before,
.geo-hexagon-1:after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
}

.geo-hexagon-1:before {
    bottom: 100%;
    border-bottom: 14.43px solid rgba(255, 255, 255, 0.1);
}

.geo-hexagon-1:after {
    top: 100%;
    border-top: 14.43px solid rgba(255, 255, 255, 0.1);
}

/* Floating Icons */
.floating-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    color: rgba(255, 255, 255, 0.4);
    font-size: 1.5rem;
    animation: floatingOrbs 12s ease-in-out infinite;
    transition: all 0.3s ease;
}

[data-theme="dark"] .floating-icon {
    color: rgba(31, 41, 55, 0.4);
}

.floating-icon:hover {
    color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

[data-theme="dark"] .floating-icon:hover {
    color: rgba(31, 41, 55, 0.8);
}

.icon-1 {
    top: 25%;
    left: 8%;
    animation-delay: 0s;
}

.icon-2 {
    top: 65%;
    right: 12%;
    animation-delay: 3s;
}

.icon-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 6s;
}

.icon-4 {
    top: 35%;
    right: 25%;
    animation-delay: 9s;
}

/* Premium Glass Morphism Buttons */
.btn {
    padding: 18px 36px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 0.05em;
    transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    border: 2px solid var(--glass-border);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    min-width: 180px;
    text-transform: uppercase;
    backdrop-filter: blur(30px);
    background: var(--glass-bg);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.05);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .btn-primary {
    background:
        linear-gradient(135deg, rgba(31, 41, 55, 0.25) 0%, rgba(31, 41, 55, 0.1) 100%);
    color: #1f2937;
    border: 2px solid rgba(31, 41, 55, 0.3);
    text-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
    transform: translateY(-6px) scale(1.05);
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.2) 100%);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .btn-primary:hover {
    background:
        linear-gradient(135deg, rgba(31, 41, 55, 0.35) 0%, rgba(31, 41, 55, 0.2) 100%);
    box-shadow:
        0 20px 60px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(31, 41, 55, 0.4),
        0 0 0 1px rgba(31, 41, 55, 0.1);
}

.btn-secondary {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.2);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .btn-secondary {
    background:
        linear-gradient(135deg, rgba(31, 41, 55, 0.15) 0%, rgba(31, 41, 55, 0.05) 100%);
    color: rgba(31, 41, 55, 0.9);
    border: 2px solid rgba(31, 41, 55, 0.2);
}

.btn-secondary:hover {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-6px) scale(1.05);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

[data-theme="dark"] .btn-secondary:hover {
    background:
        linear-gradient(135deg, rgba(31, 41, 55, 0.25) 0%, rgba(31, 41, 55, 0.15) 100%);
    border-color: rgba(31, 41, 55, 0.4);
    box-shadow:
        0 20px 60px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(31, 41, 55, 0.3);
    color: #1f2937;
}

.btn span {
    margin-right: 8px;
}

.btn i {
    transition: transform 0.3s ease;
}

.btn:hover i {
    transform: translateX(5px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
    }
}

/* Samsung Style Hero Animations */
@keyframes heroTitleReveal {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes heroSubtitleReveal {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes heroDescReveal {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes heroButtonsReveal {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmerBackground {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

@keyframes cardFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 50%;
    }
    50% {
        background-position: 50% 100%;
    }
    75% {
        background-position: 0% 50%;
    }
}

@keyframes floatingOrbs {
    0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.3;
    }
    25% {
        transform: translate(20px, -30px) scale(1.1);
        opacity: 0.5;
    }
    50% {
        transform: translate(-15px, -20px) scale(0.9);
        opacity: 0.4;
    }
    75% {
        transform: translate(10px, 15px) scale(1.05);
        opacity: 0.6;
    }
}

@keyframes shimmerWave {
    0%, 100% {
        transform: translateX(-100%) skewX(-15deg);
        opacity: 0;
    }
    50% {
        transform: translateX(100%) skewX(-15deg);
        opacity: 0.1;
    }
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-100px) rotate(180deg);
        opacity: 0.3;
    }
}

@keyframes geometricRotate {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

/* Premium Sections with Glass Effects */
section {
    padding: 140px 0;
    position: relative;
    overflow: hidden;
}

section:nth-child(even) {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] section:nth-child(even) {
    background:
        linear-gradient(135deg, rgba(31, 41, 55, 0.05) 0%, rgba(31, 41, 55, 0.02) 100%);
    border-top: 1px solid rgba(31, 41, 55, 0.1);
    border-bottom: 1px solid rgba(31, 41, 55, 0.1);
}

section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.01) 50%, transparent 70%);
    pointer-events: none;
    animation: floatingOrbs 25s ease-in-out infinite;
}

[data-theme="dark"] section::before {
    background:
        radial-gradient(circle at 20% 20%, rgba(31, 41, 55, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(31, 41, 55, 0.03) 0%, transparent 50%),
        linear-gradient(45deg, transparent 30%, rgba(31, 41, 55, 0.01) 50%, transparent 70%);
}

.section-title {
    font-size: clamp(2rem, 6vw, 3.5rem);
    text-align: center;
    margin-bottom: 4rem;
    font-weight: 100;
    color: var(--text-primary);
    position: relative;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.section-title::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--text-primary), transparent);
    opacity: 0.3;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--text-primary), transparent);
    opacity: 0.2;
}

/* About Section */
.about {
    background: var(--background-secondary);
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.8;
}

.about-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 3rem;
}

.stat {
    text-align: center;
    position: relative;
    padding: 20px;
    border-radius: 10px;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--shadow);
}

.stat h3 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.stat p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.stat-icon {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    color: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.animate-text {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-text:nth-child(2) {
    animation-delay: 0.3s;
}

/* Samsung Style Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 4rem;
}

.stat-card {
    background:
        linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .stat-card {
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow:
        0 8px 32px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(0, 0, 0, 0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.6s ease;
}

[data-theme="dark"] .stat-card::before {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, transparent 50%);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 24px 48px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .stat-card:hover {
    box-shadow:
        0 24px 48px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 0, 0, 0.2);
}

.stat-number {
    font-size: 3rem;
    font-weight: 100;
    color: var(--text-primary);
    margin-bottom: 1rem;
    letter-spacing: -0.02em;
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 4px;
}

.stat-number .counter {
    font-weight: 200;
}

.stat-number .plus {
    font-size: 2rem;
    opacity: 0.7;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.95rem;
    font-weight: 500;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    margin-bottom: 1rem;
}

.stat-card .stat-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

[data-theme="dark"] .stat-card .stat-icon {
    background: rgba(0, 0, 0, 0.1);
}

.stat-card:hover .stat-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1) rotate(10deg);
    color: var(--text-primary);
}

[data-theme="dark"] .stat-card:hover .stat-icon {
    background: rgba(0, 0, 0, 0.2);
}

/* Timeline */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 2px;
    background: var(--border-color);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -1px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    background: inherit;
    width: 50%;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    top: 15px;
    right: -10px;
    z-index: 1;
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -10px;
}

.timeline-content {
    padding: 20px 30px;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    box-shadow: 0 4px 6px var(--shadow);
}

.timeline-content h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.timeline-content h4 {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.timeline-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: bold;
}

/* Skills Section */
.skills {
    background: var(--background-secondary);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 3rem;
}

/* Samsung Style Skill Cards */
.skill-card {
    background:
        linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
    padding: 40px 30px;
    border-radius: 24px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .skill-card {
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow:
        0 8px 32px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(0, 0, 0, 0.1);
}

.skill-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

[data-theme="dark"] .skill-card::before {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.05) 100%);
}

.skill-card:hover::before {
    opacity: 1;
}

.skill-card:hover {
    transform: translateY(-16px) scale(1.03);
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .skill-card:hover {
    box-shadow:
        0 32px 64px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 0, 0, 0.2);
}

.skill-icon {
    font-size: 3.5rem;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    z-index: 2;
    opacity: 0.9;
}

.skill-card:hover .skill-icon {
    transform: scale(1.15) rotate(8deg);
    opacity: 1;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Progress Bars */
.skill-progress {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    margin-top: 1rem;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
    width: 0%;
    transition: width 2s ease-in-out;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.skill-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.skill-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Photography Section */
.photography-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.photography-intro p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 3rem;
}

/* Samsung Style Photo Grid */
.photo-item {
    aspect-ratio: 1;
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .photo-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow:
        0 8px 32px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(0, 0, 0, 0.1);
}

.photo-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: 2;
}

[data-theme="dark"] .photo-item::before {
    background:
        linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.05) 100%);
}

.photo-item:hover::before {
    opacity: 1;
}

.photo-placeholder {
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, var(--background-secondary) 0%, var(--background-primary) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
}

.photo-item:hover {
    transform: translateY(-12px) scale(1.03) rotate(1deg);
    box-shadow:
        0 24px 48px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .photo-item:hover {
    box-shadow:
        0 24px 48px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 0, 0, 0.2);
}

.photo-placeholder:hover {
    background:
        linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
}

.photo-placeholder i {
    font-size: 2.5rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    opacity: 0.8;
}

.photo-item:hover .photo-placeholder i {
    transform: scale(1.2) rotate(10deg);
    color: var(--text-primary);
    opacity: 1;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.photo-placeholder p {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.photo-item:hover .photo-placeholder p {
    color: var(--text-primary);
    transform: translateY(-2px);
}

/* Contact Section */
.contact {
    background: var(--background-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    margin-top: 3rem;
}

.contact-info h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.contact-methods {
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.contact-method i {
    margin-right: 1rem;
    width: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--background-primary);
    color: var(--text-primary);
    border-radius: 50%;
    text-decoration: none;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--background-primary);
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background: var(--background-primary);
    border-top: 1px solid var(--border-color);
    padding: 30px 0;
    text-align: center;
}

.footer p {
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .timeline::after {
        left: 31px;
    }
    
    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .timeline-item:nth-child(even) {
        left: 0%;
    }
    
    .timeline-dot {
        left: 21px;
    }
    
    .timeline-item:nth-child(even) .timeline-dot {
        left: 21px;
    }
    
    .photo-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .about-stats {
        flex-direction: column;
        gap: 2rem;
    }
}
